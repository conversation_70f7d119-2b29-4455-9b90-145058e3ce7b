<template>
  <Dialog :open="isOpen" @update:open="handleOpenChange">
    <DialogContent class="max-w-4xl max-h-[85vh]">
      <DialogHeader>
        <DialogTitle class="flex items-center space-x-2">
          <Scissors class="h-5 w-5 text-blue-600" />
          <span>切割优化管理</span>
        </DialogTitle>
        <DialogDescription>
          与第三方切割优化系统进行数据交换，获取最优切割方案
        </DialogDescription>
      </DialogHeader>

      <!-- 进度指示器 -->
      <div class="mb-6">
        <div class="flex items-center justify-between">
          <template v-for="(step, index) in optimizationSteps" :key="step.id">
            <!-- 步骤圆圈 -->
            <div class="flex flex-col items-center">
              <div 
                class="flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300"
                :class="getStepCircleClass(step.id)"
              >
                <component 
                  :is="step.icon" 
                  class="w-4 h-4"
                  :class="getStepIconClass(step.id)"
                />
              </div>
              <div class="mt-1 text-xs text-center" :class="getStepTextClass(step.id)">
                {{ step.title }}
              </div>
            </div>
            
            <!-- 连接线 -->
            <div 
              v-if="index < optimizationSteps.length - 1"
              class="flex-1 h-0.5 mx-2 transition-all duration-300"
              :class="getStepConnectionClass(index)"
            />
          </template>
        </div>
      </div>

      <!-- 动态内容区域 -->
      <div class="max-h-[50vh] overflow-y-auto">
        <!-- 步骤1: 数据导出 -->
        <div v-if="currentStep === 'export'" class="space-y-4">
          <div class="flex items-center space-x-2 mb-3">
            <Download class="h-5 w-5 text-blue-600" />
            <h3 class="text-lg font-medium text-gray-900">数据导出</h3>
          </div>
          
          <div v-if="exportStatus === 'idle'" class="text-center py-6 border-2 border-dashed border-gray-300 rounded-lg">
            <FileText class="h-10 w-10 text-gray-400 mx-auto mb-3" />
            <div class="text-sm font-medium text-gray-900 mb-2">准备导出切割数据</div>
            <div class="text-xs text-gray-500 mb-4">
              将包含 {{ batchCount }} 个批次的排产数据导出为标准格式
            </div>
            <Button @click="$emit('export-data')" size="lg">
              <Download class="h-4 w-4 mr-2" />
              开始导出
            </Button>
          </div>

          <div v-else-if="exportStatus === 'exporting'" class="text-center py-6 border border-blue-200 bg-blue-50 rounded-lg">
            <RefreshCw class="h-8 w-8 text-blue-500 animate-spin mx-auto mb-3" />
            <div class="text-sm font-medium text-blue-900 mb-2">正在导出数据...</div>
            <div class="text-xs text-blue-700">正在整理批次信息和约束条件</div>
          </div>

          <div v-else-if="exportStatus === 'exported'" class="border border-green-200 bg-green-50 rounded-lg p-4">
            <div class="flex items-center space-x-2 mb-3">
              <CheckCircle class="h-5 w-5 text-green-600" />
              <div class="text-sm font-medium text-green-900">导出完成</div>
            </div>
            <div class="grid grid-cols-2 gap-4 text-xs text-green-700 mb-3">
              <div>导出时间: {{ formatDateTime(exportData?.exportTime) }}</div>
              <div>文件格式: Excel + JSON</div>
              <div>批次数量: {{ exportData?.batchCount }} 个</div>
              <div>文件大小: {{ formatFileSize(estimatedFileSize) }}</div>
            </div>
            <div class="flex items-center space-x-2">
              <Button variant="outline" size="sm" @click="$emit('download-file')">
                <Download class="h-3 w-3 mr-1" />
                下载
              </Button>
              <Button variant="outline" size="sm" @click="$emit('export-data')">
                <RefreshCw class="h-3 w-3 mr-1" />
                重新导出
              </Button>
            </div>
          </div>
        </div>

        <!-- 步骤2: 等待处理 -->
        <div v-else-if="currentStep === 'waiting'" class="space-y-4">
          <div class="flex items-center space-x-2 mb-3">
            <Clock class="h-5 w-5 text-orange-600" />
            <h3 class="text-lg font-medium text-gray-900">等待第三方处理</h3>
          </div>

          <div class="border border-orange-200 bg-orange-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="text-sm font-medium text-orange-900">处理进行中</div>
              <div class="text-xs text-orange-700">预计 {{ estimatedTime }} 分钟</div>
            </div>
            <div class="w-full bg-orange-200 rounded-full h-2 mb-2">
              <div
                class="bg-orange-500 h-2 rounded-full transition-all duration-1000"
                :style="{ width: `${processingProgress}%` }"
              ></div>
            </div>
            <div class="text-xs text-orange-700 mb-3">
              请将导出文件发送给第三方系统，完成后导入优化结果
            </div>

            <!-- 原型演示：模拟完成按钮 -->
            <div class="border-t border-orange-200 pt-3 mt-3">
              <div class="flex items-center justify-between">
                <div class="text-xs text-orange-600">
                  <strong>原型演示：</strong>点击下方按钮模拟第三方系统处理完成
                </div>
                <Button
                  @click="$emit('simulate-complete')"
                  size="sm"
                  variant="outline"
                  class="border-orange-300 text-orange-700 hover:bg-orange-100"
                >
                  <Zap class="h-3 w-3 mr-1" />
                  模拟完成
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3: 结果导入 -->
        <div v-else-if="currentStep === 'import'" class="space-y-4">
          <div class="flex items-center space-x-2 mb-3">
            <Upload class="h-5 w-5 text-purple-600" />
            <h3 class="text-lg font-medium text-gray-900">结果导入</h3>
          </div>
          
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <Upload class="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <div class="text-sm font-medium text-gray-900 mb-1">导入优化结果</div>
            <div class="text-xs text-gray-500 mb-3">支持 Excel (.xlsx) 和 JSON (.json) 格式</div>
            
            <input
              ref="fileInput"
              type="file"
              accept=".xlsx,.json"
              @change="handleFileSelect"
              class="hidden"
            />
            
            <Button @click="$refs.fileInput?.click()" variant="outline">
              <Upload class="h-4 w-4 mr-2" />
              选择文件
            </Button>
          </div>
        </div>

        <!-- 步骤4: 验证完成 -->
        <div v-else-if="currentStep === 'completed'" class="space-y-4">
          <div class="flex items-center space-x-2 mb-3">
            <CheckCircle class="h-5 w-5 text-green-600" />
            <h3 class="text-lg font-medium text-gray-900">优化完成</h3>
          </div>
          
          <div class="border border-green-200 bg-green-50 rounded-lg p-4">
            <div class="grid grid-cols-3 gap-4 text-center mb-4">
              <div>
                <div class="text-lg font-bold text-green-600">+{{ improvements?.utilizationImproved || 0 }}%</div>
                <div class="text-xs text-green-700">利用率提升</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-600">¥{{ formatCurrency(improvements?.costSaved || 0) }}</div>
                <div class="text-xs text-green-700">成本节约</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-600">{{ improvements?.timeSaved || 0 }}h</div>
                <div class="text-xs text-green-700">时间节约</div>
              </div>
            </div>
            
            <div class="flex items-center justify-center space-x-3">
              <Button @click="$emit('view-results')" variant="outline" size="sm">
                <Eye class="h-4 w-4 mr-2" />
                查看详情
              </Button>
              <Button @click="$emit('proceed-to-final')" size="sm">
                <ArrowRight class="h-4 w-4 mr-2" />
                进入最终确认
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="flex items-center justify-between pt-4 border-t">
        <div class="text-xs text-gray-500">
          {{ getStepDescription() }}
        </div>
        <div class="flex items-center space-x-2">
          <Button variant="outline" @click="$emit('close')">
            关闭
          </Button>
          <Button v-if="canProceedToNext" @click="proceedToNext">
            下一步
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  Scissors, Download, Clock, Upload, CheckCircle,
  FileText, RefreshCw, Eye, ArrowRight, Zap
} from 'lucide-vue-next';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import type { CuttingExportData, CuttingImprovements } from '@/types/scheduling';

interface Props {
  isOpen: boolean;
  currentStep: 'export' | 'waiting' | 'import' | 'completed';
  exportStatus: 'idle' | 'exporting' | 'exported' | 'error';
  importStatus: 'idle' | 'importing' | 'imported' | 'error';
  exportData?: CuttingExportData;
  improvements?: CuttingImprovements;
  batchCount: number;
  estimatedTime: number;
}

interface Emits {
  (e: 'close'): void;
  (e: 'export-data'): void;
  (e: 'download-file'): void;
  (e: 'import-result', file: File): void;
  (e: 'view-results'): void;
  (e: 'proceed-to-final'): void;
  (e: 'simulate-complete'): void;
  (e: 'update:open', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 优化步骤定义
const optimizationSteps = [
  { id: 'export', title: '导出', icon: Download },
  { id: 'waiting', title: '处理', icon: Clock },
  { id: 'import', title: '导入', icon: Upload },
  { id: 'completed', title: '完成', icon: CheckCircle }
];

// 处理进度（模拟）
const processingProgress = ref(0);

// 预估文件大小
const estimatedFileSize = computed(() => {
  return props.batchCount * 1024 + 10240; // 简单估算
});

// 是否可以进入下一步
const canProceedToNext = computed(() => {
  return props.currentStep === 'export' && props.exportStatus === 'exported';
});

// 样式计算函数
const getStepCircleClass = (stepId: string) => {
  const currentIndex = optimizationSteps.findIndex(step => step.id === props.currentStep);
  const stepIndex = optimizationSteps.findIndex(step => step.id === stepId);
  
  if (stepIndex < currentIndex) {
    return 'border-green-500 bg-green-500';
  } else if (stepIndex === currentIndex) {
    return 'border-blue-500 bg-blue-500';
  } else {
    return 'border-gray-300 bg-white';
  }
};

const getStepIconClass = (stepId: string) => {
  const currentIndex = optimizationSteps.findIndex(step => step.id === props.currentStep);
  const stepIndex = optimizationSteps.findIndex(step => step.id === stepId);
  
  return stepIndex <= currentIndex ? 'text-white' : 'text-gray-400';
};

const getStepTextClass = (stepId: string) => {
  const currentIndex = optimizationSteps.findIndex(step => step.id === props.currentStep);
  const stepIndex = optimizationSteps.findIndex(step => step.id === stepId);
  
  if (stepIndex === currentIndex) {
    return 'text-blue-600 font-medium';
  } else if (stepIndex < currentIndex) {
    return 'text-green-600';
  } else {
    return 'text-gray-500';
  }
};

const getStepConnectionClass = (index: number) => {
  const currentIndex = optimizationSteps.findIndex(step => step.id === props.currentStep);
  
  if (index < currentIndex) {
    return 'bg-green-500';
  } else {
    return 'bg-gray-300';
  }
};

// 工具函数
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '--';
  return new Date(dateTime).toLocaleString('zh-CN');
};

const formatFileSize = (bytes: number) => {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
};

const formatCurrency = (amount: number) => {
  return amount.toLocaleString('zh-CN');
};

const getStepDescription = () => {
  const descriptions = {
    export: '导出排产数据给第三方切割优化系统',
    waiting: '等待第三方系统完成切割优化计算',
    import: '导入第三方系统返回的优化结果',
    completed: '切割优化完成，可以进入最终确认阶段'
  };
  return descriptions[props.currentStep];
};

// 事件处理
const handleOpenChange = (value: boolean) => {
  if (!value) {
    emit('close');
  }
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    emit('import-result', file);
  }
};

const proceedToNext = () => {
  // 根据当前步骤决定下一步操作
  if (props.currentStep === 'export' && props.exportStatus === 'exported') {
    // 可以手动切换到等待步骤，或者提供其他操作
  }
};
</script>
